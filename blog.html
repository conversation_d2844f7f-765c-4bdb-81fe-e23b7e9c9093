<!DOCTYPE html>
<html lang="zh-CN" class="scroll-smooth">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>暮光玻璃博客 | 未来设计实验室</title>
    <script>
      tailwind.config = {
        darkMode: "class",
        theme: {
          extend: {
            fontFamily: {
              display: ["Inter", "Noto Sans SC", "ui-sans-serif", "system-ui"],
            },
            colors: {
              brand: {
                50: "#eef2ff",
                100: "#e0e7ff",
                200: "#c7d2fe",
                300: "#a5b4fc",
                400: "#6366f1",
                500: "#4f46e5",
                600: "#4338ca",
                700: "#3730a3",
                800: "#312e81",
                900: "#1e1b4b",
              },
              accent: {
                400: "#34d399",
                500: "#10b981",
                600: "#059669",
              },
              warning: {
                400: "#fbbf24",
                500: "#f59e0b",
              },
            },
            boxShadow: {
              glow: "0 25px 60px -18px rgba(79, 70, 229, 0.45)",
            },
          },
        },
      };
      
    </script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Noto+Sans+SC:wght@400;500;700&display=swap"
      rel="stylesheet"
    />
    <style>
      :root {
        color-scheme: light;
        --page-base: #f8fafc;
        --page-layer-1: radial-gradient(at 18% 18%, rgba(99, 102, 241, 0.22) 0%, transparent 60%);
        --page-layer-2: radial-gradient(at 82% 68%, rgba(56, 189, 248, 0.18) 0%, transparent 55%);
        --glass-bg: rgba(255, 255, 255, 0.78);
        --glass-border: rgba(148, 163, 184, 0.35);
        --glass-pill-bg: rgba(255, 255, 255, 0.72);
        --glass-pill-border: rgba(148, 163, 184, 0.35);
        --bubble-indigo: rgba(99, 102, 241, 0.28);
        --bubble-cyan: rgba(56, 189, 248, 0.22);
        --bubble-rose: rgba(244, 114, 182, 0.18);
        --bubble-soft: rgba(226, 232, 240, 0.38);
      }

      :root.dark {
        color-scheme: dark;
        --page-base: #0f172a;
        --page-layer-1: radial-gradient(at 16% 20%, rgba(99, 102, 241, 0.38) 0%, transparent 65%);
        --page-layer-2: radial-gradient(at 80% 70%, rgba(6, 182, 212, 0.28) 0%, transparent 60%);
        --glass-bg: rgba(15, 23, 42, 0.6);
        --glass-border: rgba(148, 163, 184, 0.18);
        --glass-pill-bg: rgba(148, 163, 184, 0.16);
        --glass-pill-border: rgba(148, 163, 184, 0.25);
        --bubble-indigo: rgba(79, 70, 229, 0.42);
        --bubble-cyan: rgba(56, 189, 248, 0.32);
        --bubble-rose: rgba(244, 63, 94, 0.28);
        --bubble-soft: rgba(15, 23, 42, 0.55);
      }

      body {
        font-family: "Inter", "Noto Sans SC", ui-sans-serif, system-ui;
        background:
          var(--page-layer-1),
          var(--page-layer-2),
          var(--page-base);
        transition: background 0.6s ease, color 0.4s ease;
      }

      .glass-panel {
        background: var(--glass-bg);
        border: 1px solid var(--glass-border);
        backdrop-filter: blur(30px);
        transition: background 0.4s ease, border-color 0.4s ease, box-shadow 0.4s ease;
      }

      .glass-pill {
        background: var(--glass-pill-bg);
        border: 1px solid var(--glass-pill-border);
        backdrop-filter: blur(24px);
        transition: background 0.4s ease, border-color 0.4s ease, color 0.4s ease;
      }

      .glass-muted {
        color: rgba(71, 85, 105, 0.78);
      }

      :root.dark .glass-muted {
        color: rgba(226, 232, 240, 0.72);
      }

      .background-layer {
        pointer-events: none;
        position: absolute;
        inset: 0;
        overflow: hidden;
      }

      .background-bubble {
        position: absolute;
        border-radius: 50%;
        filter: blur(120px);
        opacity: 0.75;
      }

      .bubble-indigo {
        top: -8rem;
        left: -6rem;
        width: 24rem;
        height: 24rem;
        background: var(--bubble-indigo);
      }

      .bubble-cyan {
        top: 18rem;
        right: -8rem;
        width: 28rem;
        height: 28rem;
        background: var(--bubble-cyan);
      }

      .bubble-rose {
        bottom: -6rem;
        left: 40%;
        width: 26rem;
        height: 26rem;
        background: var(--bubble-rose);
        transform: translateX(-50%);
      }

      .bubble-soft {
        top: 35%;
        left: 25%;
        width: 18rem;
        height: 18rem;
        background: var(--bubble-soft);
      }

      .floating {
        animation: float 6s ease-in-out infinite;
      }

      @keyframes float {
        0%,
        100% {
          transform: translateY(0px);
        }
        50% {
          transform: translateY(-10px);
        }
      }

      .category-tag.active {
        border-color: rgba(99, 102, 241, 0.45);
        background: rgba(99, 102, 241, 0.14);
        color: #4338ca;
        box-shadow: 0 10px 30px -12px rgba(79, 70, 229, 0.45);
        transform: translateY(-1px) scale(1.05);
      }

      :root.dark .category-tag.active {
        border-color: rgba(99, 102, 241, 0.6);
        background: rgba(79, 70, 229, 0.22);
        color: rgba(199, 210, 254, 1);
      }

      .post-card {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .post-card:hover {
        transform: translateY(-4px) scale(1.02);
        box-shadow: 0 20px 50px -12px rgba(79, 70, 229, 0.35);
      }

      .reading-progress {
        position: fixed;
        top: 0;
        left: 0;
        width: 0%;
        height: 3px;
        background: linear-gradient(90deg, #4f46e5, #10b981, #f59e0b);
        z-index: 9999;
        transition: width 0.3s ease;
      }

      .pulse-dot {
        animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
      }

      @keyframes pulse {
        0%, 100% {
          opacity: 1;
        }
        50% {
          opacity: .5;
        }
      }

      .gradient-text {
        background: linear-gradient(135deg, #4f46e5, #10b981, #f59e0b);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        animation: gradient-shift 3s ease-in-out infinite;
      }

      @keyframes gradient-shift {
        0%, 100% { filter: hue-rotate(0deg); }
        50% { filter: hue-rotate(90deg); }
      }

      .sparkle {
        position: relative;
        overflow: hidden;
      }

      .sparkle::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
        transition: left 0.6s ease;
      }

      .sparkle:hover::before {
        left: 100%;
      }
    </style>
  </head>
  <body class="relative min-h-screen text-slate-900 transition-colors duration-500 dark:text-slate-100">
    <div class="reading-progress"></div>
    <div class="background-layer">
      <span class="background-bubble bubble-indigo"></span>
      <span class="background-bubble bubble-cyan"></span>
      <span class="background-bubble bubble-rose"></span>
      <span class="background-bubble bubble-soft"></span>
    </div>

    <div class="relative mx-auto flex min-h-screen max-w-6xl flex-col px-6 pb-16 pt-10 lg:px-10">
      <header class="glass-panel flex flex-wrap items-center justify-between rounded-2xl px-6 py-4 shadow-glow">
        <div class="flex items-center gap-3">
          <span class="floating flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-indigo-400 to-purple-600 text-xl shadow-lg">✨</span>
          <div>
            <h1 class="gradient-text text-2xl font-bold tracking-tight">未来设计实验室</h1>
            <p class="text-sm glass-muted flex items-center gap-2">
              <span class="pulse-dot h-2 w-2 rounded-full bg-accent-400"></span>
              探索数字体验的无限可能
            </p>
          </div>
        </div>
        <div class="mt-4 flex flex-wrap items-center gap-3 sm:mt-0">
          <button
            id="themeToggle"
            class="glass-pill flex items-center gap-2 rounded-full px-4 py-2 text-sm font-medium text-slate-800 transition hover:bg-white/90 dark:text-white/90 dark:hover:bg-white/20"
          >
            <span class="h-2 w-2 rounded-full bg-amber-400"></span>
            日间模式
          </button>
          <a
            href="#subscribe"
            class="rounded-full bg-indigo-500 px-4 py-2 text-sm font-medium text-white shadow-lg shadow-brand-500/40 transition hover:bg-indigo-400"
            >订阅</a
          >
        </div>
      </header>

      <main class="mt-12 flex-1 space-y-16">
        <section class="grid gap-10 lg:grid-cols-[1.2fr_0.8fr]" data-animate>
          <article class="glass-panel rounded-3xl p-10 shadow-glow">
            <p class="text-sm uppercase tracking-[0.3em] text-accent-500 dark:text-accent-400 flex items-center gap-2">
              <span class="h-1 w-8 bg-gradient-to-r from-accent-500 to-brand-500 rounded-full"></span>
              FEATURED STORY
            </p>
            <h2 class="mt-5 text-4xl font-bold text-slate-900 dark:text-white md:text-[2.8rem] leading-tight">
              AI 驱动的设计系统：重新定义创作流程
            </h2>
            <p class="mt-6 text-lg leading-relaxed glass-muted">
              深入探索如何将人工智能融入现代设计工作流程，从概念构思到最终实现。我们分享实战经验，包括智能组件生成、自动化样式系统、以及如何在保持创意控制的同时提升设计效率。
            </p>
            <div class="mt-8 flex flex-wrap items-center gap-4 text-sm glass-muted">
              <span class="glass-pill rounded-full px-4 py-2 text-xs font-medium uppercase tracking-[0.2em] bg-gradient-to-r from-accent-500/20 to-brand-500/20 border-accent-400/30">AI & 设计</span>
              <span class="flex items-center gap-1">
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                12 分钟阅读
              </span>
              <span class="flex items-center gap-1">
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                2024 年 12 月 25 日
              </span>
              <span class="flex items-center gap-1 text-accent-500">
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
                1.2K 点赞
              </span>
            </div>
            <div class="mt-10 flex flex-wrap gap-4">
              <a
                href="#"
                class="sparkle inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-3 text-sm font-semibold text-white shadow-lg shadow-brand-500/40 transition-all hover:shadow-xl hover:shadow-brand-500/60 hover:scale-105"
              >
                立即阅读
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 transition-transform group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3" />
                </svg>
              </a>
              <a
                href="#timeline"
                class="inline-flex items-center gap-2 text-sm font-medium text-indigo-500 transition hover:text-indigo-600 dark:text-brand-100 dark:hover:text-white"
              >
                查看项目时间线 →
              </a>
            </div>
          </article>

          <aside class="space-y-6" data-animate>
            <div class="glass-panel rounded-3xl px-6 py-8 shadow-glow">
              <div class="flex items-center justify-between">
                <p class="text-sm font-medium text-slate-800 dark:text-white">选择分类</p>
                <div class="flex gap-2 text-xs">
                  <button class="category-tag glass-pill rounded-full px-3 py-1 text-slate-800 transition hover:bg-white dark:text-white dark:hover:bg-white/20" data-category="全部">全部</button>
                  <button class="category-tag glass-pill rounded-full px-3 py-1 text-slate-600 transition hover:bg-white dark:text-white/70 dark:hover:bg-white/20" data-category="设计">设计</button>
                  <button class="category-tag glass-pill rounded-full px-3 py-1 text-slate-600 transition hover:bg-white dark:text-white/70 dark:hover:bg-white/20" data-category="产品">产品</button>
                  <button class="category-tag glass-pill rounded-full px-3 py-1 text-slate-600 transition hover:bg-white dark:text-white/70 dark:hover:bg-white/20" data-category="技术">技术</button>
                </div>
              </div>
              <div class="glass-panel mt-6 grid grid-cols-3 gap-4 rounded-2xl px-5 py-6">
                <div class="text-center">
                  <p class="text-xs font-medium uppercase tracking-wide text-slate-600 dark:text-slate-300">浏览量</p>
                  <p class="mt-2 text-xl font-bold text-indigo-600 dark:text-brand-200">28.5K</p>
                  <p class="text-xs text-accent-500">+12.5%</p>
                </div>
                <div class="text-center">
                  <p class="text-xs font-medium uppercase tracking-wide text-slate-600 dark:text-slate-300">订阅数</p>
                  <p class="mt-2 text-xl font-bold text-accent-500">2.8K</p>
                  <p class="text-xs text-accent-500">+8.2%</p>
                </div>
                <div class="text-center">
                  <p class="text-xs font-medium uppercase tracking-wide text-slate-600 dark:text-slate-300">互动率</p>
                  <p class="mt-2 text-xl font-bold text-warning-500">15.3%</p>
                  <p class="text-xs text-accent-500">+5.7%</p>
                </div>
              </div>
            </div>

            <div class="space-y-4" id="postList">
              <div class="post-card glass-panel rounded-3xl p-6 shadow-glow transition duration-300 hover:border-indigo-400/60" data-category="设计" data-animate>
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <h3 class="text-lg font-semibold text-slate-900 dark:text-white group-hover:text-brand-600 transition">玻璃拟态设计的未来趋势</h3>
                    <p class="mt-2 text-sm glass-muted">探索玻璃质感在 2024 年的演进方向，从微交互到空间计算界面的应用。</p>
                  </div>
                  <span class="ml-4 flex h-8 w-8 items-center justify-center rounded-lg bg-brand-100 text-brand-600 dark:bg-brand-900/50 dark:text-brand-300">🎨</span>
                </div>
                <div class="mt-4 flex items-center justify-between text-xs glass-muted">
                  <div class="flex items-center gap-2">
                    <span class="font-semibold uppercase tracking-[0.2em] text-brand-500 dark:text-brand-300">设计</span>
                    <span class="h-1 w-1 rounded-full bg-current opacity-50"></span>
                    <span>7 分钟阅读</span>
                  </div>
                  <span class="text-accent-500">热门 🔥</span>
                </div>
              </div>
              <div class="post-card glass-panel rounded-3xl p-6 shadow-glow transition duration-300 hover:border-indigo-400/60" data-category="产品" data-animate>
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <h3 class="text-lg font-semibold text-slate-900 dark:text-white group-hover:text-brand-600 transition">AI 产品设计的伦理思考</h3>
                    <p class="mt-2 text-sm glass-muted">在设计 AI 产品时，如何平衡用户体验与隐私保护，建立负责任的产品设计框架。</p>
                  </div>
                  <span class="ml-4 flex h-8 w-8 items-center justify-center rounded-lg bg-accent-100 text-accent-600 dark:bg-accent-900/50 dark:text-accent-300">🤖</span>
                </div>
                <div class="mt-4 flex items-center justify-between text-xs glass-muted">
                  <div class="flex items-center gap-2">
                    <span class="font-semibold uppercase tracking-[0.2em] text-accent-500 dark:text-accent-300">产品</span>
                    <span class="h-1 w-1 rounded-full bg-current opacity-50"></span>
                    <span>10 分钟阅读</span>
                  </div>
                  <span class="text-brand-500">新发布 ✨</span>
                </div>
              </div>
              <div class="post-card glass-panel rounded-3xl p-6 shadow-glow transition duration-300 hover:border-indigo-400/60" data-category="技术" data-animate>
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <h3 class="text-lg font-semibold text-slate-900 dark:text-white group-hover:text-brand-600 transition">WebGL 与 CSS 的完美结合</h3>
                    <p class="mt-2 text-sm glass-muted">深入探索如何将 3D 图形技术融入现代 Web 设计，创造沉浸式用户体验。</p>
                  </div>
                  <span class="ml-4 flex h-8 w-8 items-center justify-center rounded-lg bg-warning-100 text-warning-600 dark:bg-warning-900/50 dark:text-warning-300">⚡</span>
                </div>
                <div class="mt-4 flex items-center justify-between text-xs glass-muted">
                  <div class="flex items-center gap-2">
                    <span class="font-semibold uppercase tracking-[0.2em] text-warning-500 dark:text-warning-300">技术</span>
                    <span class="h-1 w-1 rounded-full bg-current opacity-50"></span>
                    <span>15 分钟阅读</span>
                  </div>
                  <span class="text-warning-500">进阶 💡</span>
                </div>
              </div>
            </div>
          </aside>
        </section>

        <section class="grid gap-8 md:grid-cols-3" id="recent">
          <article class="glass-panel rounded-3xl p-6 shadow-glow transition duration-300 hover:border-indigo-400/60" data-animate>
            <p class="text-xs uppercase tracking-[0.25em] text-indigo-500 dark:text-brand-100">TUTORIAL</p>
            <h3 class="mt-3 text-xl font-semibold text-slate-900 dark:text-white">用 5 分钟部署你的 AI Blog Copilot</h3>
            <p class="mt-3 text-sm glass-muted">轻松搭建写作助手，让博客产出效率翻倍。</p>
            <a class="mt-5 inline-flex items-center gap-2 text-sm font-medium text-indigo-500 hover:text-indigo-600 dark:text-brand-100 dark:hover:text-white" href="#">了解更多 →</a>
          </article>
          <article class="glass-panel rounded-3xl p-6 shadow-glow transition duration-300 hover:border-indigo-400/60" data-animate>
            <p class="text-xs uppercase tracking-[0.25em] text-indigo-500 dark:text-brand-100">INSIGHT</p>
            <h3 class="mt-3 text-xl font-semibold text-slate-900 dark:text-white">Prompt 工程如何影响品牌调性</h3>
            <p class="mt-3 text-sm glass-muted">示例 Prompt 模板与 Tone 卡片，助力统一风格。</p>
            <a class="mt-5 inline-flex items-center gap-2 text-sm font-medium text-indigo-500 hover:text-indigo-600 dark:text-brand-100 dark:hover:text-white" href="#">阅读洞察 →</a>
          </article>
          <article class="glass-panel rounded-3xl p-6 shadow-glow transition duration-300 hover:border-indigo-400/60" data-animate>
            <p class="text-xs uppercase tracking-[0.25em] text-indigo-500 dark:text-brand-100">CASE STUDY</p>
            <h3 class="mt-3 text-xl font-semibold text-slate-900 dark:text-white">从 0 到 1 推出多语言客服机器人</h3>
            <p class="mt-3 text-sm glass-muted">分享数据准备、评测维度与上线复盘。</p>
            <a class="mt-5 inline-flex items-center gap-2 text-sm font-medium text-indigo-500 hover:text-indigo-600 dark:text-brand-100 dark:hover:text-white" href="#">查看案例 →</a>
          </article>
        </section>

        <section class="glass-panel rounded-3xl p-10 shadow-glow" data-animate>
          <div class="flex flex-col gap-10 lg:flex-row lg:items-center lg:justify-between">
            <div class="lg:max-w-xl">
              <p class="text-xs uppercase tracking-[0.35em] text-indigo-500 dark:text-brand-100">GLASS QUOTE</p>
              <h2 class="mt-4 text-3xl font-semibold text-slate-900 dark:text-white">“界面不只是像素，而是一种呼吸节奏。”</h2>
              <p class="mt-4 text-sm glass-muted">
                设计总监 伊藤绘里香 分享她的玻璃拟态创作心得：刻意保留空间感、纹理对比与合理的互动反馈，才能让用户感到轻盈、安心且沉浸。
              </p>
            </div>
            <div class="glass-panel rounded-3xl p-6 text-sm glass-muted shadow-glow">
              <p>
                ✓ 透明层搭配朦胧色块<br />
                ✓ 模糊半径随层次调整<br />
                ✓ 文案节奏呼应视觉留白
              </p>
            </div>
          </div>
        </section>

        <section id="timeline" class="space-y-10" data-animate>
          <div class="flex items-center justify-between">
            <h2 class="text-3xl font-semibold text-slate-900 dark:text-white">发布记事</h2>
            <span class="text-sm glass-muted">追踪我们的设计实验迭代</span>
          </div>
          <div class="relative pl-8 before:absolute before:left-4 before:top-0 before:h-full before:w-[1px] before:bg-slate-200 dark:before:bg-white/15">
            <div class="relative mb-10 glass-panel rounded-3xl p-6 shadow-glow" data-animate>
              <span class="absolute -left-[46px] top-6 flex h-8 w-8 items-center justify-center rounded-full bg-indigo-500 text-sm font-bold text-white shadow-glow">05</span>
              <h3 class="text-lg font-semibold text-slate-900 dark:text-white">2024.05 · Glass Starter 套件上线</h3>
              <p class="mt-3 text-sm glass-muted">发布 12 份玻璃拟态组件，支持 Figma 与 React，帮助团队快速搭建视觉原型。</p>
            </div>
            <div class="relative mb-10 glass-panel rounded-3xl p-6 shadow-glow" data-animate>
              <span class="absolute -left-[46px] top-6 flex h-8 w-8 items-center justify-center rounded-full bg-indigo-500 text-sm font-bold text-white shadow-glow">04</span>
              <h3 class="text-lg font-semibold text-slate-900 dark:text-white">2024.04 · GlassOps 监控模板</h3>
              <p class="mt-3 text-sm glass-muted">新增观测指标与异常通知策略，确保玻璃风格在多端表现一致。</p>
            </div>
            <div class="relative glass-panel rounded-3xl p-6 shadow-glow" data-animate>
              <span class="absolute -left-[46px] top-6 flex h-8 w-8 items-center justify-center rounded-full bg-indigo-500 text-sm font-bold text-white shadow-glow">03</span>
              <h3 class="text-lg font-semibold text-slate-900 dark:text-white">2024.03 · Glass Design Camp</h3>
              <p class="mt-3 text-sm glass-muted">线下工作坊回顾：从情绪板到交互动效，深挖玻璃拟态体验的落地流程。</p>
            </div>
          </div>
        </section>

        <section class="grid gap-8 lg:grid-cols-[0.85fr_1.15fr]" data-animate>
          <div class="glass-panel rounded-3xl p-8 shadow-glow">
            <p class="text-xs uppercase tracking-[0.35em] text-indigo-500 dark:text-brand-100">RESOURCE PACK</p>
            <h3 class="mt-3 text-2xl font-semibold text-slate-900 dark:text-white">下载玻璃界面素材包</h3>
            <p class="mt-4 text-sm glass-muted">
              包含 30 枚渐变光斑、20 种卡片样式、12 组按钮组件和标准动效曲线，帮助你快速构建玻璃质感界面。
            </p>
            <a
              href="#"
              class="mt-6 inline-flex items-center gap-2 rounded-full bg-indigo-500/90 px-6 py-3 text-sm font-semibold text-white transition hover:bg-indigo-400"
              >立即获取</a
            >
          </div>
          <div class="grid gap-6 md:grid-cols-2">
            <div class="glass-panel rounded-3xl bg-gradient-to-br from-white/65 via-white/40 to-transparent p-6 shadow-glow dark:from-white/10 dark:via-white/5" data-animate>
              <p class="text-xs uppercase tracking-[0.25em] text-indigo-500 dark:text-brand-100">PLAYLIST</p>
              <h4 class="mt-2 text-lg font-semibold text-slate-900 dark:text-white">设计灵感歌单</h4>
              <p class="mt-2 text-sm glass-muted">精选氛围音乐，陪你在夜半打磨界面。</p>
              <button class="mt-4 inline-flex items-center gap-2 text-sm font-medium text-indigo-500 hover:text-indigo-600 dark:text-brand-100 dark:hover:text-white">播放试听 →</button>
            </div>
            <div class="glass-panel rounded-3xl bg-gradient-to-br from-indigo-200/50 via-indigo-100/30 to-transparent p-6 shadow-glow dark:from-brand-500/20 dark:via-brand-500/10" data-animate>
              <p class="text-xs uppercase tracking-[0.25em] text-indigo-500 dark:text-brand-50">COLOR LAB</p>
              <h4 class="mt-2 text-lg font-semibold text-slate-900 dark:text-white">柔光配色实验室</h4>
              <p class="mt-2 text-sm glass-muted">动态生成玻璃背景渐变，支持导出 CSS 与 PNG。</p>
              <button class="mt-4 inline-flex items-center gap-2 text-sm font-medium text-indigo-500 hover:text-indigo-600 dark:text-brand-100 dark:hover:text-white">体验工具 →</button>
            </div>
            <div class="glass-panel rounded-3xl p-6 shadow-glow md:col-span-2" data-animate>
              <p class="text-xs uppercase tracking-[0.25em] text-indigo-500 dark:text-brand-100">FOCUS LIST</p>
              <h4 class="mt-2 text-lg font-semibold text-slate-900 dark:text-white">本周推荐灵感</h4>
              <ul class="mt-4 space-y-3 text-sm glass-muted">
                <li>◎ 纽约艺术学院《透明感的叙事设计》</li>
                <li>◎ Sorbet UI：玻璃风格移动端组件库</li>
                <li>◎ Motion Canvas：玻璃界面微动效手册</li>
              </ul>
            </div>
          </div>
        </section>

        <section id="subscribe" class="relative overflow-hidden rounded-3xl" data-animate>
          <div class="glass-panel relative p-10 shadow-glow">
            <div class="pointer-events-none absolute -right-10 -top-10 h-40 w-40 rounded-full bg-indigo-200/60 blur-3xl dark:bg-brand-500/35"></div>
            <div class="pointer-events-none absolute -left-12 bottom-6 h-32 w-60 rounded-full bg-rose-200/50 blur-3xl dark:bg-rose-500/20"></div>
            <div class="relative z-10 grid gap-8 lg:grid-cols-[1.1fr_0.9fr]">
              <div>
                <h2 class="text-3xl font-semibold text-slate-900 dark:text-white">订阅玻璃简报</h2>
                <p class="mt-3 text-sm glass-muted">
                  每两周推送精选文章、工具链模板与设计灵感，帮助你打造令人惊叹的体验。我们也会分享 GlassOps 性能监控技巧，让质感与效率并存。
                </p>
                <form class="mt-6 flex flex-col gap-3 sm:flex-row">
                  <input
                    type="email"
                    placeholder="输入你的邮箱"
                    class="w-full rounded-full border border-slate-200/70 bg-white/90 px-5 py-3 text-sm text-slate-800 placeholder-slate-400 backdrop-blur-md focus:outline-none focus:ring-2 focus:ring-indigo-400 dark:border-white/20 dark:bg-white/15 dark:text-white dark:placeholder-white/50"
                  />
                  <button
                    type="submit"
                    class="rounded-full bg-indigo-500 px-6 py-3 text-sm font-semibold text-white shadow-lg shadow-brand-500/40 transition hover:bg-indigo-400"
                  >
                    立即订阅
                  </button>
                </form>
              </div>
              <div class="glass-panel rounded-3xl p-6 text-sm glass-muted shadow-glow">
                <h3 class="text-base font-semibold text-slate-900 dark:text-white">订阅礼遇</h3>
                <ul class="mt-4 space-y-2">
                  <li>• 免费获取 Glass Starter UI 套件</li>
                  <li>• 加入 Discord 私享实验室</li>
                  <li>• 每季线上分享会优先席位</li>
                </ul>
              </div>
            </div>
          </div>
        </section>
      </main>

      <footer class="glass-panel mt-16 flex flex-col gap-6 rounded-2xl px-6 py-6 text-sm shadow-glow sm:flex-row sm:items-center sm:justify-between">
        <div>
          <p class="font-medium text-slate-900 dark:text-white">© 2024 Twilight Studio. Crafted with glassmorphism.</p>
          <p class="mt-1 text-xs glass-muted">用心打造：设计、产品、技术三位一体的实验室。</p>
        </div>
        <div class="flex gap-4 text-sm font-medium text-indigo-500 dark:text-brand-100">
          <a href="#" class="transition hover:text-indigo-600 dark:hover:text-white">关于我们</a>
          <a href="#" class="transition hover:text-indigo-600 dark:hover:text-white">联系</a>
          <a href="#" class="transition hover:text-indigo-600 dark:hover:text-white">隐私政策</a>
        </div>
      </footer>
    </div>

    <button
      id="backToTop"
      class="fixed bottom-8 right-8 hidden rounded-full border border-slate-200/80 bg-white/90 p-3 text-slate-700 shadow-lg backdrop-blur-xl transition hover:bg-indigo-500 hover:text-white dark:border-white/20 dark:bg-white/10 dark:text-white"
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
        <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 15.75 12 8.25l7.5 7.5" />
      </svg>
    </button>

    <script>
      const root = document.documentElement;
      const toggleButton = document.getElementById("themeToggle");
      const tags = document.querySelectorAll(".category-tag");
      const posts = document.querySelectorAll(".post-card");
      const backToTop = document.getElementById("backToTop");
      const revealElements = document.querySelectorAll("[data-animate]");
      const readingProgress = document.querySelector(".reading-progress");
      const THEME_KEY = "glass-theme";

      // Reading progress functionality
      function updateReadingProgress() {
        const scrollTop = window.pageYOffset;
        const docHeight = document.body.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;
        readingProgress.style.width = scrollPercent + '%';
      }

      function getPreferredMode() {
        const stored = localStorage.getItem(THEME_KEY);
        if (stored === "light" || stored === "dark") return stored;
        return window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
      }
      
      

      function applyMode(mode) {
        root.classList.toggle("dark", mode === "dark");
        toggleButton.innerHTML =
          mode === "dark"
            ? '<span class="h-2 w-2 rounded-full bg-amber-400"></span> 日间模式'
            : '<span class="h-2 w-2 rounded-full bg-lime-500"></span> 夜间模式';
        localStorage.setItem(THEME_KEY, mode);
      }

      toggleButton.addEventListener("click", () => {
        const nextMode = root.classList.contains("dark") ? "light" : "dark";
        applyMode(nextMode);
      });

      tags.forEach((tag) => {
        tag.addEventListener("click", () => {
          const category = tag.dataset.category;
          tags.forEach((t) => t.classList.remove("active"));
          tag.classList.add("active");

          // Add haptic feedback effect
          tag.style.transform = 'scale(0.95)';
          setTimeout(() => {
            tag.style.transform = '';
          }, 150);

          posts.forEach((post, index) => {
            const match = category === "全部" || post.dataset.category === category;
            
            // Staggered animation for filtered posts
            setTimeout(() => {
              post.classList.toggle("opacity-30", !match);
              post.classList.toggle("scale-[0.97]", !match);
              post.style.pointerEvents = match ? "auto" : "none";
              
              if (match) {
                post.style.animationDelay = `${index * 0.1}s`;
                post.classList.add('animate-pulse');
                setTimeout(() => post.classList.remove('animate-pulse'), 600);
              }
            }, index * 50);
          });
        });
      });

      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              entry.target.classList.add("opacity-100", "translate-y-0");
              entry.target.classList.remove("opacity-0", "translate-y-6");
              observer.unobserve(entry.target);
            }
          });
        },
        {
          threshold: 0.2,
        }
      );


      revealElements.forEach((el) => {
        el.classList.add("opacity-0", "translate-y-6", "transition", "duration-700", "ease-out");
        observer.observe(el);
      });

      // Enhanced scroll handling with reading progress and back to top
      let ticking = false;
      function handleScroll() {
        updateReadingProgress();
        
        if (window.scrollY > 320) {
          backToTop.classList.remove("hidden");
        } else {
          backToTop.classList.add("hidden");
        }
        
        ticking = false;
      }
      
      window.addEventListener("scroll", () => {
        if (!ticking) {
          requestAnimationFrame(handleScroll);
          ticking = true;
        }
      });

  

      backToTop.addEventListener("click", () => {
        window.scrollTo({ top: 0, behavior: "smooth" });
      });

      // Enhanced post card interactions
      posts.forEach((post) => {
        post.addEventListener('mouseenter', () => {
          post.style.transform = 'translateY(-4px) scale(1.02)';
        });
        
        post.addEventListener('mouseleave', () => {
          post.style.transform = '';
        });
        
        // Add click animation
        post.addEventListener('click', () => {
          post.style.transform = 'scale(0.98)';
          setTimeout(() => {
            post.style.transform = 'translateY(-4px) scale(1.02)';
          }, 100);
        });
      });

      // Smooth theme transition with visual feedback
      toggleButton.addEventListener("mouseover", () => {
        toggleButton.style.transform = 'scale(1.05)';
      });
      
      toggleButton.addEventListener("mouseleave", () => {
        toggleButton.style.transform = '';
      });

      // Initialize
      applyMode(getPreferredMode());
      const defaultTag = document.querySelector('.category-tag[data-category="全部"]');
      if (defaultTag) {
        defaultTag.classList.add("active");
      }
      
      // Add page load animation
      window.addEventListener('load', () => {
        document.body.style.opacity = '0';
        document.body.style.transform = 'translateY(20px)';
        document.body.style.transition = 'all 0.6s ease';
        
        setTimeout(() => {
          document.body.style.opacity = '1';
          document.body.style.transform = 'translateY(0)';
        }, 100);
      });
    </script>
  </body>
</html>
