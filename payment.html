<!DOCTYPE html>
<html lang="zh-CN" class="scroll-smooth">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>玻璃支付中心</title>
    <script>
      tailwind.config = {
        darkMode: "class",
        theme: {
          extend: {
            fontFamily: {
              display: ["Inter", "Noto Sans SC", "ui-sans-serif", "system-ui"],
            },
            colors: {
              brand: {
                500: "#4f46e5",
                600: "#4338ca",
                700: "#312e81",
              },
              wechat: "#1aad19",
              alipay: "#1677ff",
            },
            borderRadius: {
              card: "2rem",
            },
            borderWidth: {
              card: "1px",
            },
            divider: {
              card: "1px solid rgba(148, 163, 184, 0.35)",
            },
            boxShadow: {
              glow: "0 30px 80px -28px rgba(79, 70, 229, 0.45)",
            },
          },
        },
      };
    </script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Noto+Sans+SC:wght@400;500;700&display=swap"
      rel="stylesheet"
    />
    <style>
      :root {
        color-scheme: light;
        --page-base: #f8fafc;
        --page-gradient-1: radial-gradient(80% 80% at 15% 20%, rgba(99, 102, 241, 0.25) 0%, transparent 65%);
        --page-gradient-2: radial-gradient(70% 70% at 78% 65%, rgba(56, 189, 248, 0.22) 0%, transparent 60%);
        --glass-bg: rgba(255, 255, 255, 0.78);
        --glass-border: rgba(148, 163, 184, 0.35);
        --muted-text: rgba(71, 85, 105, 0.78);
      }

      :root.dark {
        color-scheme: dark;
        --page-base: #0f172a;
        --page-gradient-1: radial-gradient(100% 100% at 10% 10%, rgba(79, 70, 229, 0.42) 0%, transparent 68%);
        --page-gradient-2: radial-gradient(80% 80% at 85% 70%, rgba(45, 212, 191, 0.28) 0%, transparent 60%);
        --glass-bg: rgba(15, 23, 42, 0.6);
        --glass-border: rgba(148, 163, 184, 0.24);
        --muted-text: rgba(226, 232, 240, 0.72);
      }

      body {
        font-family: "Inter", "Noto Sans SC", ui-sans-serif, system-ui;
        background:
          var(--page-gradient-1),
          var(--page-gradient-2),
          var(--page-base);
        transition: background 0.6s ease, color 0.4s ease;
      }

      .glass-card {
        background: var(--glass-bg);
        border: 1px solid var(--glass-border);
        backdrop-filter: blur(28px);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .glass-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 25px 50px -12px rgba(79, 70, 229, 0.25);
        border-color: rgba(79, 70, 229, 0.3);
      }

      .glass-muted {
        color: var(--muted-text);
      }

      .pay-option {
        position: relative;
        overflow: hidden;
      }

      .pay-option:before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.1), transparent);
        transition: left 0.6s ease;
      }

      .pay-option:hover:before {
        left: 100%;
      }

      .pay-option.active {
        border-color: rgba(79, 70, 229, 0.8);
        background: linear-gradient(135deg, var(--glass-bg) 0%, rgba(79, 70, 229, 0.05) 100%);
        box-shadow: 0 8px 32px rgba(79, 70, 229, 0.3), 0 0 0 1px rgba(79, 70, 229, 0.2) inset;
        transform: translateY(-1px) scale(1.02);
      }

      .pay-option.active .pay-icon {
        transform: scale(1.1);
        box-shadow: 0 8px 16px rgba(79, 70, 229, 0.3);
      }

      .qr-placeholder {
        background: linear-gradient(135deg, rgba(148, 163, 184, 0.35), rgba(99, 102, 241, 0.35));
        border-radius: 16px;
        border: 1px dashed rgba(148, 163, 184, 0.4);
        position: relative;
        overflow: hidden;
        animation: qrPulse 3s ease-in-out infinite;
      }

      .qr-placeholder:before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
        animation: qrShine 4s ease-in-out infinite;
      }

      @keyframes qrPulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.02); }
      }

      @keyframes qrShine {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
      }

      .pay-icon {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .form-input {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        background: var(--glass-bg);
      }

      .form-input:focus {
        transform: translateY(-1px);
        box-shadow: 0 10px 25px -5px rgba(79, 70, 229, 0.25), 0 0 0 3px rgba(79, 70, 229, 0.1);
      }

      .loading-dots {
        display: inline-block;
      }

      .loading-dots:after {
        content: '.';
        animation: dots 1.5s steps(3, end) infinite;
      }

      @keyframes dots {
        0%, 20% { color: rgba(0,0,0,0); text-shadow: .25em 0 0 rgba(0,0,0,0), .5em 0 0 rgba(0,0,0,0); }
        40% { color: currentColor; text-shadow: .25em 0 0 rgba(0,0,0,0), .5em 0 0 rgba(0,0,0,0); }
        60% { text-shadow: .25em 0 0 currentColor, .5em 0 0 rgba(0,0,0,0); }
        80%, 100% { text-shadow: .25em 0 0 currentColor, .5em 0 0 currentColor; }
      }

      .slide-in {
        animation: slideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
      }

      @keyframes slideIn {
        0% {
          opacity: 0;
          transform: translateY(20px);
        }
        100% {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .success-checkmark {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #10b981;
        position: relative;
        display: inline-block;
      }

      .success-checkmark:after {
        content: '';
        position: absolute;
        top: 6px;
        left: 8px;
        width: 6px;
        height: 10px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);\n      }\n\n      /* 移动端适配 */\n      @media (max-width: 768px) {\n        .glass-card:hover {\n          transform: none;\n        }\n\n        .pay-option:hover:before {\n          left: -100%;\n        }\n\n        .pay-option.active {\n          transform: translateY(0) scale(1);\n        }\n\n        .qr-placeholder {\n          height: 120px;\n          width: 120px;\n          animation: none;\n        }\n\n        .form-input:focus {\n          transform: none;\n        }\n      }\n\n      @media (max-width: 640px) {\n        main {\n          grid-template-columns: 1fr !important;\n        }\n\n        header {\n          flex-direction: column;\n          align-items: flex-start;\n        }\n\n        .pay-methods {\n          grid-template-columns: 1fr !important;\n        }\n\n        .qr-section {\n          grid-template-columns: 1fr !important;\n          text-align: center;\n        }\n\n        .qr-placeholder {\n          margin: 0 auto;\n        }\n      }
      }
    </style>
  </head>
  <body class="min-h-screen text-slate-900 transition-colors duration-500 dark:text-slate-100">
    <div class="absolute inset-0 pointer-events-none">
      <div class="absolute left-10 top-24 h-60 w-60 rounded-full bg-brand-500/25 blur-3xl"></div>
      <div class="absolute right-16 bottom-20 h-72 w-72 rounded-full bg-cyan-300/25 blur-3xl"></div>
      <div class="absolute left-1/2 top-1/2 h-56 w-56 -translate-x-1/2 -translate-y-1/2 rounded-full bg-purple-500/20 blur-3xl"></div>
    </div>

    <div class="relative mx-auto flex min-h-screen max-w-6xl flex-col px-6 pb-16 pt-12 lg:px-10">
      <header class="glass-card flex flex-col gap-4 rounded-2xl px-6 py-5 shadow-glow sm:flex-row sm:items-center sm:justify-between">
        <div>
          <p class="text-sm uppercase tracking-[0.35em] text-indigo-500 dark:text-indigo-200">PAYMENT</p>
          <h1 class="mt-2 text-3xl font-semibold text-slate-900 dark:text-white">玻璃支付中心</h1>
          <p class="glass-muted mt-2 text-sm">为您的会员订单选择合适的支付方式，支持快速扫码与银行卡支付。</p>
        </div>
        <div class="flex items-center gap-3 self-start rounded-2xl bg-white/20 px-4 py-3 text-sm font-medium text-slate-800 backdrop-blur-xl dark:bg-white/10 dark:text-slate-100">
          <span class="h-8 w-8 rounded-full bg-white/60 text-center text-lg leading-8 shadow-sm dark:bg-white/20">￥</span>
          <div>
            <p class="text-xs uppercase tracking-[0.3em] text-indigo-500 dark:text-indigo-200">待支付金额</p>
            <p class="text-2xl font-semibold">¥ 699.00</p>
          </div>
        </div>
      </header>

      <main class="mt-12 grid flex-1 gap-10 lg:grid-cols-[1.1fr_0.9fr]">
        <section class="glass-card rounded-3xl p-8 shadow-glow">
          <h2 class="text-lg font-semibold text-slate-900 dark:text-white">选择支付方式</h2>
          <p class="glass-muted mt-2 text-sm">我们为你准备了支付宝、微信支付与银行卡支付，任意选择一种即可完成订单。</p>

          <div class="mt-8 grid gap-4 md:grid-cols-3">
            <button
              class="pay-option glass-card flex flex-col items-start gap-4 rounded-2xl px-5 py-6 text-left transition"
              data-method="alipay"
            >
              <span class="pay-icon flex h-14 w-14 items-center justify-center rounded-2xl bg-white/60 shadow-inner dark:bg-white/10">
                <svg class="h-11 w-11" viewBox="0 0 64 64" role="img" aria-label="支付宝">
                  <rect width="64" height="64" rx="16" fill="#1677ff"></rect>
                  <path fill="#ffffff" d="M33 14h11.5v5.8h-8l-1.4 6.2h8.9v5.3h-10l-1.9 8.1c4.8-1 8.3-1.2 10.7-1.2 5.9 0 10 2.5 10 6.9 0 5.3-5.2 9-12.9 9-6.1 0-10.8-1.9-14-5.6l3.9-3.4c2.3 2.5 5.1 3.7 9.3 3.7 3.6 0 6.2-1.5 6.2-3.7s-2-3.3-5.9-3.3c-3.6 0-7.2 0.6-11.4 1.9l-3.2-2.9 2.8-11.9h-6.1V26h7.1l1.4-6.2H18v-5.8h15.7z"></path>
                  <path fill="#ffffff" d="M12 41.1c6.5 3.5 14.1 5.3 22.4 5.3 8.6 0 16.5-1.9 23.6-5.6V48c-7.4 3.6-15.5 5.8-23.7 5.8-8 0-15.6-1.8-22.3-5.1V41.1z"></path>
                </svg>
              </span>
              <div>
                <p class="text-base font-semibold">支付宝</p>
                <p class="glass-muted text-xs">推荐扫码付款，实时到账</p>
              </div>
            </button>

            <button
              class="pay-option glass-card flex flex-col items-start gap-4 rounded-2xl px-5 py-6 text-left transition"
              data-method="wechat"
            >
              <span class="pay-icon flex h-14 w-14 items-center justify-center rounded-2xl bg-white/60 shadow-inner dark:bg-white/10">
                <svg class="h-11 w-11" viewBox="0 0 64 64" role="img" aria-label="微信支付">
                  <rect width="64" height="64" rx="16" fill="#1aad19"></rect>
                  <path fill="#ffffff" d="M32 14c-11.1 0-20.1 6.9-20.1 15.6 0 4.9 3 9.2 7.6 12l-2.2 7.9 8.2-4.4c2.4 0.7 5 1 7.6 1 11.1 0 20.1-6.9 20.1-15.6C53.2 20.9 43.1 14 32 14zm-9.8 16.1c-1.4 0-2.6-1-2.6-2.2s1.2-2.2 2.6-2.2 2.6 1 2.6 2.2-1.2 2.2-2.6 2.2zm11 0c-1.4 0-2.6-1-2.6-2.2s1.2-2.2 2.6-2.2 2.6 1 2.6 2.2-1.2 2.2-2.6 2.2z"></path>
                  <path fill="#ffffff" d="M49 31.5c7 0 12.9 4.9 12.9 11 0 3.5-2.1 6.8-5.2 8.9l1.2 5.3-6-3.2c-1.8 0.4-3.6 0.7-5.5 0.7-7 0-12.9-4.9-12.9-11S42 31.5 49 31.5zm-6.3 8.9c-1 0-1.9-0.7-1.9-1.7s0.8-1.7 1.9-1.7 1.9 0.7 1.9 1.7-0.9 1.7-1.9 1.7zm8.6 0c-1 0-1.9-0.7-1.9-1.7s0.8-1.7 1.9-1.7c1 0 1.9 0.7 1.9 1.7s-0.9 1.7-1.9 1.7z"></path>
                </svg>
              </span>
              <div>
                <p class="text-base font-semibold">微信支付</p>
                <p class="glass-muted text-xs">支持企业收款与分账</p>
              </div>
            </button>

            <button
              class="pay-option glass-card flex flex-col items-start gap-4 rounded-2xl px-5 py-6 text-left transition"
              data-method="card"
            >
              <span class="pay-icon flex h-14 w-14 items-center justify-center rounded-2xl bg-white/60 shadow-inner dark:bg-white/10">
                <svg class="h-11 w-11" viewBox="0 0 64 64" role="img" aria-label="银行卡">
                  <defs>
                    <linearGradient id="cardGradient" x1="0" x2="1" y1="0" y2="1">
                      <stop offset="0%" stop-color="#4f46e5"></stop>
                      <stop offset="100%" stop-color="#312e81"></stop>
                    </linearGradient>
                  </defs>
                  <rect width="64" height="64" rx="16" fill="url(#cardGradient)"></rect>
                  <rect x="10" y="20" width="44" height="8" rx="4" fill="rgba(255,255,255,0.65)"></rect>
                  <rect x="10" y="30" width="44" height="24" rx="10" fill="rgba(255,255,255,0.2)"></rect>
                  <rect x="16" y="36" width="12" height="8" rx="3" fill="#facc15"></rect>
                  <rect x="30" y="36" width="20" height="6" rx="3" fill="rgba(255,255,255,0.6)"></rect>
                  <rect x="30" y="44" width="18" height="6" rx="3" fill="rgba(255,255,255,0.4)"></rect>
                </svg>
              </span>
              <div>
                <p class="text-base font-semibold">银行卡</p>
                <p class="glass-muted text-xs">支持国内主流借记/信用卡</p>
              </div>
            </button>
          </div>

          <div id="methodDetails" class="mt-10 space-y-6">
            <div class="glass-card rounded-3xl p-6">
              <h3 class="text-base font-semibold text-slate-900 dark:text-white">付款说明</h3>
              <p class="glass-muted mt-2 text-sm">请选择支付方式查看具体的操作指引与收款信息。</p>
            </div>
          </div>
        </section>

        <aside class="glass-card flex flex-col gap-6 rounded-3xl p-8 shadow-glow">
          <div>
            <h2 class="text-lg font-semibold text-slate-900 dark:text-white">订单信息</h2>
            <p class="glass-muted mt-2 text-sm">若订单信息有误，请返回商城修改后重新结算。</p>
          </div>

          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <span class="glass-muted text-sm">订单号</span>
              <span class="text-sm font-medium text-slate-900 dark:text-white">TW-240531-8921</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="glass-muted text-sm">套餐名称</span>
              <span class="text-sm font-medium text-slate-900 dark:text-white">暮光创作会员 · 年度</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="glass-muted text-sm">有效期</span>
              <span class="text-sm font-medium text-slate-900 dark:text-white">2024.06.01 - 2025.05.31</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="glass-muted text-sm">原价</span>
              <span class="text-sm font-medium text-slate-900 line-through dark:text-white/70">¥ 799.00</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-slate-900 dark:text-white">优惠券</span>
              <span class="text-sm font-semibold text-emerald-400">- ¥ 100.00</span>
            </div>
            <div class="flex items-center justify-between border-t border-white/20 pt-4">
              <span class="text-base font-semibold text-slate-900 dark:text-white">应付金额</span>
              <span class="text-2xl font-bold text-indigo-500 dark:text-indigo-200">¥ 699.00</span>
            </div>
          </div>

          <div class="glass-card rounded-2xl border border-white/20 p-4">
            <p class="glass-muted text-xs">如需发票，请在支付成功后于“我的订单”中提交发票信息，我们将在 3 个工作日内开具电子发票。</p>
          </div>

          <button
            id="payButton"
            class="group relative overflow-hidden rounded-2xl bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-4 text-base font-semibold text-white shadow-lg shadow-brand-500/40 transition-all duration-300 hover:from-indigo-400 hover:to-purple-500 hover:shadow-xl hover:shadow-brand-500/50 disabled:from-slate-400 disabled:to-slate-500 disabled:shadow-none"
            disabled
          >
            <span class="relative z-10">请选择支付方式</span>
            <div class="absolute inset-0 -translate-x-full bg-gradient-to-r from-white/0 via-white/20 to-white/0 transition-transform duration-500 group-hover:translate-x-full"></div>
          </button>
        </aside>
      </main>
    </div>

    <script>
      const root = document.documentElement;
      const themePreference = localStorage.getItem('glass-theme');
      if (themePreference === 'dark') {
        root.classList.add('dark');
      }

      const methodDetails = document.getElementById('methodDetails');
      const payOptions = document.querySelectorAll('.pay-option');
      const payButton = document.getElementById('payButton');

      const methodTemplates = {
        alipay: `
          <div class="glass-card rounded-3xl p-6">
            <h3 class="text-base font-semibold text-slate-900 dark:text-white">支付宝扫码支付</h3>
            <p class="glass-muted mt-2 text-sm">打开支付宝“扫一扫”，扫描下方二维码完成支付。支付成功后页面自动更新订单状态。</p>
            <div class="mt-6 grid gap-6 md:grid-cols-[1fr_auto]">
              <div>
                <p class="text-sm font-medium text-slate-900 dark:text-white">收款方</p>
                <p class="glass-muted text-sm">Twilight Studio（杭州暮光）</p>
                <p class="mt-4 text-sm font-medium text-slate-900 dark:text-white">到账时间</p>
                <p class="glass-muted text-sm">实时到账，支持 24/7</p>
              </div>
              <div class="qr-placeholder flex h-40 w-40 items-center justify-center text-center text-xs font-medium text-indigo-100">
                支付宝专属<br />动态二维码
              </div>
            </div>
          </div>
        `,
        wechat: `
          <div class="glass-card rounded-3xl p-6">
            <h3 class="text-base font-semibold text-slate-900 dark:text-white">微信支付</h3>
            <p class="glass-muted mt-2 text-sm">支持微信钱包余额、绑定银行卡等多种渠道，若为企业账号可使用对公转账。</p>
            <div class="mt-6 grid gap-6 md:grid-cols-[1fr_auto]">
              <div>
                <p class="text-sm font-medium text-slate-900 dark:text-white">收款方</p>
                <p class="glass-muted text-sm">Twilight Studio 数字体验中心</p>
                <p class="mt-4 text-sm font-medium text-slate-900 dark:text-white">支付提示</p>
                <ul class="mt-1 space-y-2 text-sm glass-muted">
                  <li>• 扫码前请确认金额 699.00 元</li>
                  <li>• 企业付款可开具增值税专票</li>
                </ul>
              </div>
              <div class="qr-placeholder flex h-40 w-40 items-center justify-center text-center text-xs font-medium text-green-50">
                微信支付<br />安全二维码
              </div>
            </div>
          </div>
        `,
        card: `
          <div class="glass-card rounded-3xl p-6 space-y-6">
            <div>
              <h3 class="text-base font-semibold text-slate-900 dark:text-white">银行卡支付</h3>
              <p class="glass-muted mt-2 text-sm">我们采用银联安全通道，支持借记卡、信用卡以及数字人民币钱包。</p>
            </div>
            <form class="grid gap-4" id="cardForm">
              <label class="text-sm">
                <span class="glass-muted">持卡人姓名</span>
                <input
                  type="text"
                  name="cardName"
                  required
                  placeholder="请填写银行卡姓名"
                  class="form-input mt-2 w-full rounded-xl border border-white/30 px-4 py-3 text-sm text-slate-800 shadow-inner focus:border-indigo-400 focus:outline-none focus:ring-2 focus:ring-indigo-300/60 dark:border-white/20 dark:text-white"
                />
              </label>
              <label class="text-sm">
                <span class="glass-muted">银行卡号</span>
                <input
                  type="text"
                  name="cardNumber"
                  inputmode="numeric"
                  pattern="[0-9]{12,19}"
                  required
                  placeholder="请输入 12-19 位卡号"
                  class="form-input mt-2 w-full rounded-xl border border-white/30 px-4 py-3 text-sm text-slate-800 shadow-inner focus:border-indigo-400 focus:outline-none focus:ring-2 focus:ring-indigo-300/60 dark:border-white/20 dark:text-white"
                />
              </label>
              <div class="grid gap-4 sm:grid-cols-3">
                <label class="text-sm sm:col-span-1">
                  <span class="glass-muted">有效期</span>
                  <input
                    type="text"
                    name="expiry"
                    placeholder="MM/YY"
                    pattern="^(0[1-9]|1[0-2])\/[0-9]{2}$"
                    required
                    class="form-input mt-2 w-full rounded-xl border border-white/30 px-4 py-3 text-sm text-slate-800 shadow-inner focus:border-indigo-400 focus:outline-none focus:ring-2 focus:ring-indigo-300/60 dark:border-white/20 dark:text-white"
                  />
                </label>
                <label class="text-sm sm:col-span-1">
                  <span class="glass-muted">安全码</span>
                  <input
                    type="password"
                    name="cvv"
                    inputmode="numeric"
                    pattern="[0-9]{3,4}"
                    required
                    placeholder="CVV"
                    class="form-input mt-2 w-full rounded-xl border border-white/30 px-4 py-3 text-sm text-slate-800 shadow-inner focus:border-indigo-400 focus:outline-none focus:ring-2 focus:ring-indigo-300/60 dark:border-white/20 dark:text-white"
                  />
                </label>
                <label class="text-sm sm:col-span-1">
                  <span class="glass-muted">手机号</span>
                  <input
                    type="tel"
                    name="phone"
                    pattern="1[3-9][0-9]{9}"
                    required
                    placeholder="银行预留手机号"
                    class="form-input mt-2 w-full rounded-xl border border-white/30 px-4 py-3 text-sm text-slate-800 shadow-inner focus:border-indigo-400 focus:outline-none focus:ring-2 focus:ring-indigo-300/60 dark:border-white/20 dark:text-white"
                  />
                </label>
              </div>
              <p class="glass-muted text-xs">提交信息后将跳转至银行短信验证页面，未通过验证不会扣款。</p>
            </form>
          </div>
        `,
      };

      let currentMethod = null;

      function renderMethod(method) {
        // 添加淡出动画
        methodDetails.style.opacity = '0';
        methodDetails.style.transform = 'translateY(10px)';
        
        setTimeout(() => {
          methodDetails.innerHTML = methodTemplates[method];
          methodDetails.classList.add('slide-in');
          
          // 重置样式并添加淡入动画
          methodDetails.style.opacity = '1';
          methodDetails.style.transform = 'translateY(0)';
          
          payButton.disabled = false;
          const buttonText = payButton.querySelector('span');
          buttonText.innerHTML = method === 'card' ? '提交银行卡信息' : '查看二维码并完成支付';
        }, 150);
      }

      payOptions.forEach((btn) => {
        btn.addEventListener('click', () => {
          const method = btn.dataset.method;
          if (method === currentMethod) return;

          // 移除所有活跃状态
          payOptions.forEach((item) => {
            item.classList.remove('active');
            // 添加点击反馈
            item.style.transform = 'scale(0.98)';
            setTimeout(() => {
              item.style.transform = '';
            }, 100);
          });
          
          // 添加活跃状态和点击效果
          btn.classList.add('active');
          btn.style.transform = 'scale(1.02)';
          setTimeout(() => {
            btn.style.transform = '';
          }, 200);

          currentMethod = method;
          renderMethod(method);
        });
        
        // 添加鼠标悬停效果
        btn.addEventListener('mouseenter', () => {
          if (!btn.classList.contains('active')) {
            btn.style.transform = 'translateY(-2px) scale(1.01)';
          }
        });
        
        btn.addEventListener('mouseleave', () => {
          if (!btn.classList.contains('active')) {
            btn.style.transform = '';
          }
        });
      });

      // 添加支付按钮点击效果
      payButton.addEventListener('click', () => {
        if (!payButton.disabled) {
          const buttonText = payButton.querySelector('span');
          const originalText = buttonText.innerHTML;
          
          buttonText.innerHTML = '<span class="loading-dots">处理中</span>';
          payButton.disabled = true;
          
          // 模拟处理时间
          setTimeout(() => {
            if (currentMethod === 'card') {
              buttonText.innerHTML = '<span class="success-checkmark"></span> 跳转到银行页面';
            } else {
              buttonText.innerHTML = '<span class="success-checkmark"></span> 支付完成';
            }
            
            setTimeout(() => {
              buttonText.innerHTML = originalText;
              payButton.disabled = false;
            }, 2000);
          }, 3000);
        }
      });
    </script>
  </body>
</html>
