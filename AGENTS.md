# Repository Guidelines

## Project Structure & Module Organization
- Organize production code under `src/`, using subpackages such as `src/agents/` for agent logic and `src/core/` for shared utilities. Add configuration defaults in `configs/` and reusable prompts or corpora in `assets/`.
- Keep helper scripts in `scripts/` (mark them executable) and document architectural decisions in `docs/`. Unit and integration tests mirror the code layout in `tests/` (e.g., `tests/agents/test_routing.py`).
- When introducing a new module, add an `__init__.py` and update `docs/architecture.md` with a short note on intent and dependencies.

## Build, Test, and Development Commands
- `make install` — install runtime and development dependencies declared in `pyproject.toml`.
- `make lint` — run static analysis (ruff and mypy) across `src/` and `tests/`; resolve warnings before opening a PR.
- `make test` — execute the entire test suite with coverage, exporting a report to `artifacts/coverage/`.
- `make run entry=src/agents/demo.py` — run a module locally; use environment variables defined in `.env.example`.

## Coding Style & Naming Conventions
- Follow Black-compatible 4-space indentation and limit lines to 100 characters. Keep imports sorted with `ruff`'s formatter.
- Name modules with lowercase underscores (`text_router.py`); classes in PascalCase; functions and variables in snake_case.
- Document public functions with Google-style docstrings and surface expected side effects clearly.

## Testing Guidelines
- Write tests with `pytest`; use descriptive names like `test_agent_handles_empty_prompt`. Group fixtures in `tests/conftest.py` to share setup.
- Target ≥90% coverage for new code. Capture edge cases (timeouts, malformed input) and include regression tests when fixing bugs.
- Run `make test` locally before pushing; if adding async code, include `pytest.mark.asyncio` and use `anyio` helpers.

## Commit & Pull Request Guidelines
- Use conventional commits (`feat: add routing policy cache`) to keep history searchable. Squash noisy work-in-progress before merging.
- Reference issue IDs in commit bodies and PR descriptions. Include a concise summary, testing notes, and screenshots or logs when behavior changes.
- Request review from at least one other agent contributor and wait for CI to pass before merging.

## Security & Configuration Tips
- Never commit secrets; rely on `.env` files listed in `.gitignore` and provide sample values in `.env.example`.
- Review dependencies quarterly and note updates in `docs/changelog.md`. Flag third-party services that require keys in the PR description.
