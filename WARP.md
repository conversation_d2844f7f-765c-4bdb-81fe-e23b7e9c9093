# WARP.md

此文件为 WARP (warp.dev) 提供在此代码仓库中工作的指导。

## 项目概述

这是一个玻璃拟态主题的 Web 项目，具有现代化的 UI 组件和玻璃质感的视觉效果。项目包含嵌入式 CSS 和 JavaScript 的 HTML 页面，展示了具有精致视觉设计模式的博客平台和支付界面。

## 架构与结构

### 核心组件
- **玻璃设计系统**：利用 CSS 自定义属性实现玻璃拟态效果，包括背景模糊、透明层和渐变背景
- **主题系统**：支持浅色/深色模式切换，使用 CSS 变量和本地存储持久化
- **响应式布局**：基于 Tailwind CSS 工具类和自定义 CSS 构建，实现高级视觉效果
- **交互元素**：JavaScript 驱动的动画、表单验证和动态内容过滤

### 关键文件
- `blog.html` - 主博客界面，包含玻璃拟态设计、分类过滤和动画元素
- `payment.html` - 支付处理界面，支持多种支付方式选择和表单验证
- `AGENTS.md` - 开发指南和编码标准（作为项目规则）

### 设计模式
- **CSS 架构**：大量使用 CSS 自定义属性（变量）实现主题化和玻璃效果
- **布局系统**：网格和弹性盒布局，具有响应式断点
- **视觉效果**：背景模糊、渐变叠加、动画伪元素和交集观察器动画
- **组件结构**：自包含组件，具有嵌入式样式和行为

## 开发命令



### 本地开发
```bash
# 本地启动静态文件服务器（因为这些是静态 HTML 文件）
python3 -m http.server 8000
# 或者
npx serve .
```

### 文件操作
```bash
# 查看文件结构
find . -name "*.html" -o -name "*.md" | head -20

# 检查文件内容
head -50 blog.html
head -50 payment.html
```

### Git 操作
```bash
# 初始设置（仓库已存在但尚无提交）
git add .
git commit -m "初始提交：添加玻璃拟态网页界面"

# 标准工作流程
git add <文件>
git commit -m "描述性提交信息"
git push origin main
```

## 编码标准与指南

### HTML 结构
- 使用语义化 HTML5 元素
- 实现适当的 ARIA 标签以提高可访问性
- 保持一致的缩进（2个空格）
- 包含响应式设计的 meta 标签

### CSS 约定
- 遵循已建立的 CSS 自定义属性命名模式（`--glass-bg`、`--page-base` 等）
- 使用一致的边框圆角值（通常是 4px 的倍数：8px、12px、16px、20px、24px）
- 保持玻璃效果层次：主要元素的背景模糊值应为 24px-30px
- 保持过渡持续时间一致：颜色变化 0.4s，背景变化 0.6s

### JavaScript 模式
- 使用原生 JavaScript 和现代 ES6+ 特性
- 为动态内容实现适当的事件委托
- 遵循已建立的 localStorage 主题管理模式
- 使用交集观察器实现滚动触发动画

### 可访问性标准
- 确保所有交互元素具有适当的焦点状态
- 包含适当的 ARIA 标签和角色
- 保持足够的颜色对比度
- 支持键盘导航

### 性能考虑
- 最小化使用昂贵的 CSS 属性（backdrop-filter、box-shadow）
- 使用 CSS 变换进行动画而不是改变布局属性
- 在适用的地方实现适当的图像优化和懒加载
- 保持 JavaScript 执行轻量化以确保动画流畅

## 主题系统

项目使用精密的 CSS 自定义属性系统进行主题化：

### 核心变量
- `--page-base`：背景色基础
- `--glass-bg`：主要玻璃面板背景
- `--glass-border`：玻璃面板边框色
- `--glass-muted`：静音文本色

### 实现方式
- 主题偏好存储在 localStorage 中，键名为 `glass-theme`
- 自动系统主题检测作为回退
- 浅色和深色模式之间平滑过渡

## 测试与质量保证

### 手动测试清单
- [ ] 主题切换正常工作
- [ ] 跨设备尺寸的响应式行为
- [ ] 表单验证和用户反馈
- [ ] 动画性能和流畅性
- [ ] 跨浏览器兼容性（Chrome、Firefox、Safari、Edge）

### 浏览器测试
- 跨不同浏览器测试玻璃拟态效果
- 验证 backdrop-filter 支持和优雅降级
- 确保适当的移动端触摸交互

## 项目背景

此项目遵循 `AGENTS.md` 中概述的开发指南，包括：
- 4空格缩进以保持一致性
- Google 风格的函数文档
- 约定式提交消息
- 客户端代码的安全最佳实践

玻璃拟态设计系统通过透明度、模糊效果和微妙阴影来强调视觉层次，创造现代化的分层界面美学。
